<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:2000'],
            'client_id' => ['required', 'exists:clients,id', function ($attribute, $value, $fail) {
                if ($value) {
                    $client = \App\Models\Client::find($value);
                    if ($client && $client->user_id !== auth()->id()) {
                        $fail('The selected client is invalid.');
                    }
                }
            }],
            'status' => ['required', 'in:planning,active,on_hold,completed,cancelled'],
            'priority' => ['nullable', 'in:low,medium,high,urgent'],
            'start_date' => ['nullable', 'date', 'before_or_equal:due_date'],
            'due_date' => ['nullable', 'date', 'after_or_equal:start_date'],
            'budget' => ['nullable', 'numeric', 'min:0', 'max:999999999'],
            'hourly_rate' => ['nullable', 'numeric', 'min:0', 'max:999999'],
            'billing_type' => ['nullable', 'in:hourly,fixed,milestone'],
            'is_billable' => ['boolean'],
            'notes' => ['nullable', 'string', 'max:5000'],
            'custom_fields' => ['nullable', 'array'],
            'custom_fields.*' => ['string', 'max:1000'],
            'progress_percentage' => ['nullable', 'numeric', 'min:0', 'max:100'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Project name is required.',
            'name.max' => 'Project name cannot exceed 255 characters.',
            'client_id.required' => 'Please select a client.',
            'client_id.exists' => 'The selected client is invalid.',
            'status.required' => 'Project status is required.',
            'status.in' => 'Invalid project status selected.',
            'priority.in' => 'Invalid priority level selected.',
            'start_date.before_or_equal' => 'Start date must be before or equal to due date.',
            'due_date.after_or_equal' => 'Due date must be after or equal to start date.',
            'budget.numeric' => 'Budget must be a valid number.',
            'budget.min' => 'Budget cannot be negative.',
            'budget.max' => 'Budget amount is too large.',
            'hourly_rate.numeric' => 'Hourly rate must be a valid number.',
            'hourly_rate.min' => 'Hourly rate cannot be negative.',
            'hourly_rate.max' => 'Hourly rate is too large.',
            'billing_type.in' => 'Invalid billing type selected.',
            'notes.max' => 'Notes cannot exceed 5000 characters.',
            'custom_fields.array' => 'Custom fields must be an array.',
            'custom_fields.*.max' => 'Custom field value cannot exceed 1000 characters.',
            'progress_percentage.numeric' => 'Progress percentage must be a valid number.',
            'progress_percentage.min' => 'Progress percentage cannot be negative.',
            'progress_percentage.max' => 'Progress percentage cannot exceed 100.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'is_billable' => $this->boolean('is_billable', true),
        ]);

        // Set default priority if not provided
        if (!$this->filled('priority')) {
            $this->merge(['priority' => 'medium']);
        }

        // Set default billing type if not provided
        if (!$this->filled('billing_type')) {
            $this->merge(['billing_type' => 'hourly']);
        }
    }


}
