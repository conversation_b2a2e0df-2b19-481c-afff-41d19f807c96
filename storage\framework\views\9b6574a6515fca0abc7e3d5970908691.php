<?php if (isset($component)) { $__componentOriginalb74e8d81074516492afd394bc835e266 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb74e8d81074516492afd394bc835e266 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.sidebar-app','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('sidebar-app'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-bold text-gray-800">Time Tracking</h2>
                        <div class="flex space-x-3">
                            <a href="<?php echo e(route('time-tracking.create')); ?>" 
                               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                                <i class="fas fa-plus mr-2"></i>Manual Entry
                            </a>
                        </div>
                    </div>

                    <!-- Active Timer Section -->
                    <?php if($activeTimer): ?>
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse mr-3"></div>
                                    <div>
                                        <h3 class="font-medium text-green-800">Timer Running</h3>
                                        <p class="text-sm text-green-600">
                                            <?php echo e($activeTimer->project->name); ?> 
                                            <?php if($activeTimer->task): ?>
                                                - <?php echo e($activeTimer->task->title); ?>

                                            <?php endif; ?>
                                        </p>
                                        <p class="text-xs text-green-500">
                                            Started: <?php echo e($activeTimer->start_time->format('g:i A')); ?>

                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="text-lg font-mono text-green-800" id="timer-display">
                                        <?php echo e($activeTimer->getElapsedTimeAttribute()); ?>

                                    </div>
                                    <button onclick="stopTimer()" 
                                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                                        <i class="fas fa-stop mr-2"></i>Stop
                                    </button>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Quick Timer Start -->
                    <?php if(!$activeTimer && $projects->count() > 0): ?>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                            <h3 class="font-medium text-blue-800 mb-3">Quick Start Timer</h3>
                            <form id="quick-timer-form" class="flex items-end space-x-3">
                                <?php echo csrf_field(); ?>
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Project</label>
                                    <select name="project_id" required class="w-full border-gray-300 rounded-md shadow-sm">
                                        <option value="">Select Project</option>
                                        <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($project->id); ?>"><?php echo e($project->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                <div class="flex-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                    <input type="text" name="description" required 
                                           class="w-full border-gray-300 rounded-md shadow-sm"
                                           placeholder="What are you working on?">
                                </div>
                                <button type="submit" 
                                        class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors">
                                    <i class="fas fa-play mr-2"></i>Start Timer
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>

                    <!-- Time Entries List -->
                    <?php if($timeEntries->count() > 0): ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Date & Time
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Project
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Description
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Duration
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Billable
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php $__currentLoopData = $timeEntries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $entry): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <div><?php echo e($entry->start_time->format('M j, Y')); ?></div>
                                                <div class="text-xs text-gray-500">
                                                    <?php echo e($entry->start_time->format('g:i A')); ?>

                                                    <?php if($entry->end_time): ?>
                                                        - <?php echo e($entry->end_time->format('g:i A')); ?>

                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo e($entry->project->name); ?>

                                                </div>
                                                <?php if($entry->task): ?>
                                                    <div class="text-xs text-gray-500">
                                                        <?php echo e($entry->task->title); ?>

                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 text-sm text-gray-900">
                                                <?php echo e($entry->description); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo e($entry->getFormattedDurationAttribute()); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php if($entry->is_billable): ?>
                                                    <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                                        Billable
                                                    </span>
                                                <?php else: ?>
                                                    <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                                        Non-billable
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex space-x-2">
                                                    <a href="<?php echo e(route('time-tracking.edit', $entry)); ?>" 
                                                       class="text-blue-600 hover:text-blue-900">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form method="POST" action="<?php echo e(route('time-tracking.destroy', $entry)); ?>" 
                                                          class="inline" onsubmit="return confirm('Are you sure?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="text-red-600 hover:text-red-900">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-6">
                            <?php echo e($timeEntries->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-12">
                            <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <i class="fas fa-clock text-3xl text-gray-400"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No time entries yet</h3>
                            <p class="text-gray-500 mb-6">Start tracking your time to see entries here.</p>
                            <?php if($projects->count() > 0): ?>
                                <a href="<?php echo e(route('time-tracking.create')); ?>" 
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                                    <i class="fas fa-plus mr-2"></i>Add Time Entry
                                </a>
                            <?php else: ?>
                                <p class="text-sm text-gray-400">Create a project first to start tracking time.</p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script>
        // Timer functionality
        function stopTimer() {
            fetch('<?php echo e(route("time-tracking.stop")); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            });
        }

        // Quick timer form
        document.getElementById('quick-timer-form')?.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('<?php echo e(route("time-tracking.start")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            });
        });
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb74e8d81074516492afd394bc835e266)): ?>
<?php $attributes = $__attributesOriginalb74e8d81074516492afd394bc835e266; ?>
<?php unset($__attributesOriginalb74e8d81074516492afd394bc835e266); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb74e8d81074516492afd394bc835e266)): ?>
<?php $component = $__componentOriginalb74e8d81074516492afd394bc835e266; ?>
<?php unset($__componentOriginalb74e8d81074516492afd394bc835e266); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\freeligo\resources\views/time-tracking/index.blade.php ENDPATH**/ ?>