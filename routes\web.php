<?php

use App\Http\Controllers\AiAssistantController;
use App\Http\Controllers\AutomationController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\ContractController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\FileController;
use App\Http\Controllers\FollowUpController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\MultiClientDashboardController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\PortfolioGeneratorController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\TdsController;
use App\Http\Controllers\TimeEntryController;
use Illuminate\Support\Facades\Route;

// Public Pages
Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
Route::get('/about', [App\Http\Controllers\HomeController::class, 'about'])->name('about');
Route::get('/features', [App\Http\Controllers\HomeController::class, 'features'])->name('features');
Route::get('/pricing', [App\Http\Controllers\HomeController::class, 'pricing'])->name('pricing');
Route::get('/contact', [App\Http\Controllers\HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [App\Http\Controllers\HomeController::class, 'submitContact'])
    ->middleware('rate.limit:contact')
    ->name('contact.submit');
Route::get('/privacy', [App\Http\Controllers\HomeController::class, 'privacy'])->name('privacy');
Route::get('/terms', [App\Http\Controllers\HomeController::class, 'terms'])->name('terms');
Route::get('/refund', [App\Http\Controllers\HomeController::class, 'refund'])->name('refund');

// Dashboard
Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::get('/dashboard/data', [DashboardController::class, 'getData'])
    ->middleware(['auth'])
    ->name('dashboard.data');

// Profile routes
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Upgrade routes
    Route::get('/upgrade/required', function () {
        return view('upgrade.required');
    })->name('upgrade.required');

    // Advanced Features (Business Plan)
    // AI Assistant
    Route::get('/ai-assistant', [AiAssistantController::class, 'index'])->name('ai-assistant.index');
    Route::post('/ai-assistant/generate', [AiAssistantController::class, 'generate'])->name('ai-assistant.generate');
    Route::post('/ai-assistant/analyze', [AiAssistantController::class, 'analyze'])->name('ai-assistant.analyze');
    Route::post('/ai-assistant/invoice-description', [AiAssistantController::class, 'generateInvoiceDescription'])->name('ai-assistant.invoice-description');
    Route::post('/ai-assistant/business-insights', [AiAssistantController::class, 'generateBusinessInsights'])->name('ai-assistant.business-insights');

    // Multi-Client Dashboard
    Route::get('/multi-client-dashboard', [MultiClientDashboardController::class, 'index'])->name('multi-client-dashboard.index');
    Route::post('/multi-client-dashboard/compare', [MultiClientDashboardController::class, 'compare'])->name('multi-client-dashboard.compare');

    // Portfolio Generator
    Route::get('/portfolio-generator', [PortfolioGeneratorController::class, 'index'])->name('portfolio-generator.index');
    Route::post('/portfolio-generator/generate', [PortfolioGeneratorController::class, 'generate'])->name('portfolio-generator.generate');
    Route::get('/portfolio-generator/preview', [PortfolioGeneratorController::class, 'preview'])->name('portfolio-generator.preview');
    Route::get('/portfolio-generator/download', [PortfolioGeneratorController::class, 'download'])->name('portfolio-generator.download');
});

// Client Management
Route::middleware('auth')->group(function () {
    Route::resource('clients', ClientController::class);
});

// Invoice Management
Route::middleware('auth')->group(function () {
    Route::resource('invoices', InvoiceController::class);
    Route::get('invoices/{invoice}/download', [InvoiceController::class, 'download'])->name('invoices.download');
    Route::patch('invoices/{invoice}/mark-paid', [InvoiceController::class, 'markAsPaid'])->name('invoices.mark-paid');
});

// Contract Management
Route::middleware('auth')->group(function () {
    Route::resource('contracts', ContractController::class);
    Route::get('contracts/{contract}/download', [ContractController::class, 'download'])->name('contracts.download');
    Route::patch('contracts/{contract}/send', [ContractController::class, 'send'])->name('contracts.send');
    Route::patch('contracts/{contract}/mark-signed', [ContractController::class, 'markAsSigned'])->name('contracts.mark-signed');
    Route::get('contract-templates/{template}', [ContractController::class, 'getTemplate'])->name('contract-templates.show');
});

// Project Management
Route::middleware('auth')->group(function () {
    Route::resource('projects', ProjectController::class);
    Route::get('projects/{project}/dashboard', [ProjectController::class, 'dashboard'])->name('projects.dashboard');
    Route::patch('projects/{project}/status', [ProjectController::class, 'updateStatus'])->name('projects.status');

    // Project Members
    Route::post('projects/{project}/members', [ProjectController::class, 'addMember'])->name('projects.members.add');
    Route::delete('projects/{project}/members/{member}', [ProjectController::class, 'removeMember'])->name('projects.members.remove');
    Route::patch('projects/{project}/members/{member}', [ProjectController::class, 'updateMember'])->name('projects.members.update');

    // Project Tasks API
    Route::get('projects/{project}/tasks', [ProjectController::class, 'getTasks'])->name('projects.tasks.api');

    // Tasks within projects
    Route::resource('projects.tasks', TaskController::class)->except(['index']);
    Route::patch('projects/{project}/tasks/{task}/status', [TaskController::class, 'updateStatus'])->name('projects.tasks.status');
    Route::patch('projects/{project}/tasks/{task}/assign', [TaskController::class, 'assign'])->name('projects.tasks.assign');
});

// Time Tracking
Route::middleware('auth')->prefix('time-tracking')->name('time-tracking.')->group(function () {
    Route::get('/', [TimeEntryController::class, 'index'])->name('index');
    Route::get('/create', [TimeEntryController::class, 'create'])->name('create');
    Route::post('/', [TimeEntryController::class, 'store'])->name('store');
    Route::get('/{timeEntry}', [TimeEntryController::class, 'show'])->name('show');
    Route::get('/{timeEntry}/edit', [TimeEntryController::class, 'edit'])->name('edit');
    Route::put('/{timeEntry}', [TimeEntryController::class, 'update'])->name('update');
    Route::delete('/{timeEntry}', [TimeEntryController::class, 'destroy'])->name('destroy');

    // Timer controls
    Route::post('/start', [TimeEntryController::class, 'startTimer'])->name('start');
    Route::post('/stop', [TimeEntryController::class, 'stopTimer'])->name('stop');
    Route::post('/pause', [TimeEntryController::class, 'pauseTimer'])->name('pause');
    Route::get('/active', [TimeEntryController::class, 'getActiveTimer'])->name('active');

    // Reports
    Route::get('/reports/timesheet', [TimeEntryController::class, 'timesheet'])->name('reports.timesheet');
    Route::get('/reports/summary', [TimeEntryController::class, 'summary'])->name('reports.summary');
    Route::get('/reports/export', [TimeEntryController::class, 'export'])->name('reports.export');
});

// TDS Management
Route::middleware('auth')->group(function () {
    Route::get('tds', [TdsController::class, 'index'])->name('tds.index');
    Route::get('tds/{tdsRecord}', [TdsController::class, 'show'])->name('tds.show');
    Route::patch('tds/{tdsRecord}/certificate', [TdsController::class, 'updateCertificate'])->name('tds.update-certificate');
    Route::get('tds/export/excel', [TdsController::class, 'export'])->name('tds.export');
    Route::get('tds/summary/view', [TdsController::class, 'summary'])->name('tds.summary');
});

// Follow-up Management
Route::middleware('auth')->group(function () {
    Route::resource('followups', FollowUpController::class);
    Route::patch('followups/{followUp}/mark-sent', [FollowUpController::class, 'markAsSent'])->name('followups.mark-sent');
    Route::get('followup-templates', [FollowUpController::class, 'getTemplates'])->name('followup-templates');
});

// File Management
Route::middleware('auth')->group(function () {
    Route::get('files', [FileController::class, 'index'])->name('files.index');
    Route::get('files/create', [FileController::class, 'create'])->name('files.create');
    Route::post('files', [FileController::class, 'store'])->name('files.store')->middleware('file.limits');
    Route::get('files/{file}', [FileController::class, 'show'])->name('files.show');
    Route::get('files/{file}/edit', [FileController::class, 'edit'])->name('files.edit');
    Route::put('files/{file}', [FileController::class, 'update'])->name('files.update');
    Route::delete('files/{file}', [FileController::class, 'destroy'])->name('files.destroy');
    Route::get('files/{file}/download', [FileController::class, 'download'])->name('files.download');
    Route::get('files/picker', [FileController::class, 'picker'])->name('files.picker');
});

// Automation Management
Route::middleware('auth')->prefix('automation')->name('automation.')->group(function () {
    Route::get('/dashboard', [AutomationController::class, 'dashboard'])->name('dashboard');
    Route::get('/workflows', [AutomationController::class, 'workflows'])->name('workflows');
    Route::post('/workflows', [AutomationController::class, 'createWorkflow'])->name('workflows.create');
    Route::put('/workflows/{workflow}', [AutomationController::class, 'updateWorkflow'])->name('workflows.update');
    Route::delete('/workflows/{workflow}', [AutomationController::class, 'deleteWorkflow'])->name('workflows.delete');
    Route::patch('/workflows/{workflow}/toggle', [AutomationController::class, 'toggleWorkflow'])->name('workflows.toggle');
    Route::post('/workflows/{workflow}/test', [AutomationController::class, 'testWorkflow'])->name('workflows.test');

    Route::get('/templates', [AutomationController::class, 'templates'])->name('templates');
    Route::post('/templates', [AutomationController::class, 'createTemplate'])->name('templates.create');
    Route::put('/templates/{template}', [AutomationController::class, 'updateTemplate'])->name('templates.update');

    Route::get('/preferences', [AutomationController::class, 'preferences'])->name('preferences');
    Route::put('/preferences', [AutomationController::class, 'updatePreferences'])->name('preferences.update');

    Route::post('/initialize-defaults', [AutomationController::class, 'initializeDefaults'])->name('initialize');
    Route::get('/analytics', [AutomationController::class, 'analyticsApi'])->name('analytics');
});

// Subscription Management
Route::middleware('auth')->group(function () {
    Route::get('subscriptions', [SubscriptionController::class, 'index'])->name('subscriptions.index');
    Route::get('subscriptions/plans', [SubscriptionController::class, 'plans'])->name('subscriptions.plans');
    Route::post('subscriptions/subscribe/{plan}', [SubscriptionController::class, 'subscribe'])->name('subscriptions.subscribe');
    Route::post('subscriptions/upgrade/{plan}', [SubscriptionController::class, 'upgrade'])->name('subscriptions.upgrade');
    Route::post('subscriptions/cancel', [SubscriptionController::class, 'cancel'])->name('subscriptions.cancel');
    Route::get('subscriptions/billing', [SubscriptionController::class, 'billing'])->name('subscriptions.billing');
});

// Payment Management
Route::middleware(['auth', 'rate.limit:payment'])->group(function () {
    Route::get('payment/{payment}/gateway', [PaymentController::class, 'gateway'])->name('payment.gateway');
    Route::post('payment/{payment}/paypal', [PaymentController::class, 'paypal'])->name('payment.paypal');
    Route::post('payment/{payment}/razorpay', [PaymentController::class, 'razorpay'])->name('payment.razorpay');
    Route::get('payment/{payment}/success', [PaymentController::class, 'success'])->name('payment.success');
    Route::get('payment/{payment}/failure', [PaymentController::class, 'failure'])->name('payment.failure');
});

// Payment Webhooks (no auth required but with signature validation)
Route::post('webhooks/paypal', [PaymentController::class, 'paypalWebhook'])
    ->middleware('rate.limit:webhook')
    ->name('webhooks.paypal');
Route::post('webhooks/razorpay', [PaymentController::class, 'razorpayWebhook'])
    ->middleware('rate.limit:webhook')
    ->name('webhooks.razorpay');

// Admin Routes
Route::middleware(['auth', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
    // File Management
    Route::resource('files', App\Http\Controllers\Admin\FileManagementController::class);
    Route::get('files/{file}/download', [App\Http\Controllers\Admin\FileManagementController::class, 'download'])->name('files.download');
    Route::get('files/stats', [App\Http\Controllers\Admin\FileManagementController::class, 'stats'])->name('files.stats');
});

// Admin AI Settings (superadmin only)
Route::middleware(['auth', 'role:superadmin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('ai-settings', [App\Http\Controllers\Admin\AISettingsController::class, 'index'])->name('ai-settings');
    Route::post('ai-settings/update-provider', [App\Http\Controllers\Admin\AISettingsController::class, 'updateProvider'])->name('ai-settings.update-provider');
    Route::post('ai-settings/test-provider', [App\Http\Controllers\Admin\AISettingsController::class, 'testProvider'])->name('ai-settings.test-provider');
});

require __DIR__.'/auth.php';
