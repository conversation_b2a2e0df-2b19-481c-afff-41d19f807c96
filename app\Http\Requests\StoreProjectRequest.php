<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreProjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:2000'],
            'client_type' => ['required', 'in:traditional,user'],
            'client_id' => ['required_if:client_type,traditional', 'exists:clients,id', function ($attribute, $value, $fail) {
                if ($value) {
                    $client = \App\Models\Client::find($value);
                    if ($client && $client->user_id !== auth()->id()) {
                        $fail('The selected client is invalid.');
                    }
                }
            }],
            'client_user_id' => ['required_if:client_type,user', 'exists:users,id', function ($attribute, $value, $fail) {
                if ($value) {
                    $user = \App\Models\User::find($value);
                    if ($user && !in_array($user->user_type, ['client', 'both'])) {
                        $fail('The selected user is not a client.');
                    }
                    if ($value === auth()->id()) {
                        $fail('You cannot assign yourself as a client.');
                    }
                }
            }],
            'status' => ['required', 'in:planning,active,on_hold,completed,cancelled'],
            'priority' => ['nullable', 'in:low,medium,high,urgent'],
            'start_date' => ['nullable', 'date', 'before_or_equal:due_date'],
            'due_date' => ['nullable', 'date', 'after_or_equal:start_date'],
            'budget' => ['nullable', 'numeric', 'min:0', 'max:999999999'],
            'hourly_rate' => ['nullable', 'numeric', 'min:0', 'max:999999'],
            'billing_type' => ['nullable', 'in:hourly,fixed,milestone'],
            'is_billable' => ['boolean'],
            'notes' => ['nullable', 'string', 'max:5000'],
            'custom_fields' => ['nullable', 'array'],
            'custom_fields.*' => ['string', 'max:1000'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Project name is required.',
            'name.max' => 'Project name cannot exceed 255 characters.',
            'client_type.required' => 'Please select a client type.',
            'client_type.in' => 'Invalid client type selected.',
            'client_id.required_if' => 'Please select a client when using traditional client type.',
            'client_id.exists' => 'The selected client is invalid.',
            'client_user_id.required_if' => 'Please select a client user when using user client type.',
            'client_user_id.exists' => 'The selected client user is invalid.',
            'status.required' => 'Project status is required.',
            'status.in' => 'Invalid project status selected.',
            'priority.in' => 'Invalid priority level selected.',
            'start_date.before_or_equal' => 'Start date must be before or equal to due date.',
            'due_date.after_or_equal' => 'Due date must be after or equal to start date.',
            'budget.numeric' => 'Budget must be a valid number.',
            'budget.min' => 'Budget cannot be negative.',
            'budget.max' => 'Budget amount is too large.',
            'hourly_rate.numeric' => 'Hourly rate must be a valid number.',
            'hourly_rate.min' => 'Hourly rate cannot be negative.',
            'hourly_rate.max' => 'Hourly rate is too large.',
            'billing_type.in' => 'Invalid billing type selected.',
            'notes.max' => 'Notes cannot exceed 5000 characters.',
            'custom_fields.array' => 'Custom fields must be an array.',
            'custom_fields.*.max' => 'Custom field value cannot exceed 1000 characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'user_id' => auth()->id(),
            'is_billable' => $this->boolean('is_billable', true),
        ]);

        // Set default values
        if (!$this->filled('status')) {
            $this->merge(['status' => 'planning']);
        }

        if (!$this->filled('priority')) {
            $this->merge(['priority' => 'medium']);
        }

        if (!$this->filled('billing_type')) {
            $this->merge(['billing_type' => 'hourly']);
        }

        if (!$this->filled('start_date')) {
            $this->merge(['start_date' => now()->toDateString()]);
        }
    }

    /**
     * Get the validated data from the request.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);

        // Ensure only one client type is set
        if ($validated['client_type'] === 'traditional') {
            $validated['client_user_id'] = null;
        } else {
            $validated['client_id'] = null;
        }

        return $validated;
    }
}
