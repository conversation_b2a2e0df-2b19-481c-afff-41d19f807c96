<?php

namespace App\Repositories;

use App\Models\Project;
use App\Models\User;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ProjectRepository
{
    /**
     * Get projects for user with filtering and pagination
     */
    public function getProjectsForUser(int $userId, $request = null): LengthAwarePaginator
    {
        $query = User::find($userId)->getAllInvolvedProjects()
            ->with(['client', 'clientUser', 'tasks', 'timeEntries', 'projectMembers.user']);

        // Apply filters if request is provided
        if ($request) {
            $this->applyFilters($query, $request);
        }

        return $query->latest()->paginate(10);
    }

    /**
     * Apply filters to the query
     */
    protected function applyFilters(Builder $query, $request): void
    {
        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by client
        if ($request->filled('client_id')) {
            $query->where('client_id', $request->client_id);
        }

        // Filter by client user
        if ($request->filled('client_user_id')) {
            $query->where('client_user_id', $request->client_user_id);
        }

        // Filter by priority
        if ($request->filled('priority')) {
            $query->where('priority', $request->priority);
        }

        // Filter by date range
        if ($request->filled('start_date')) {
            $query->whereDate('start_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate('due_date', '<=', $request->end_date);
        }

        // Search by name or description
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Sort by
        if ($request->filled('sort_by')) {
            $sortBy = $request->sort_by;
            $sortOrder = $request->filled('sort_order') ? $request->sort_order : 'desc';
            
            switch ($sortBy) {
                case 'name':
                    $query->orderBy('name', $sortOrder);
                    break;
                case 'status':
                    $query->orderBy('status', $sortOrder);
                    break;
                case 'due_date':
                    $query->orderBy('due_date', $sortOrder);
                    break;
                case 'budget':
                    $query->orderBy('budget', $sortOrder);
                    break;
                case 'progress':
                    $query->orderBy('progress_percentage', $sortOrder);
                    break;
                default:
                    $query->latest();
            }
        }
    }

    /**
     * Get project statistics for user
     */
    public function getStatsForUser(int $userId): array
    {
        $user = User::find($userId);
        $projects = $user->getAllInvolvedProjects();

        $stats = [
            'total_projects' => $projects->count(),
            'active_projects' => $projects->where('status', 'active')->count(),
            'completed_projects' => $projects->where('status', 'completed')->count(),
            'on_hold_projects' => $projects->where('status', 'on_hold')->count(),
            'overdue_projects' => $projects->where('status', '!=', 'completed')
                ->where('due_date', '<', now())
                ->count(),
        ];

        // Calculate total budget and spent amounts
        $totalBudget = $projects->sum('budget');
        $totalSpent = $projects->sum(function ($project) {
            return $project->timeEntries->sum('billable_amount');
        });

        $stats['total_budget'] = $totalBudget;
        $stats['total_spent'] = $totalSpent;
        $stats['budget_utilization'] = $totalBudget > 0 ? round(($totalSpent / $totalBudget) * 100, 2) : 0;

        // Calculate average project completion time
        $completedProjects = $projects->where('status', 'completed')
            ->whereNotNull('completed_date')
            ->whereNotNull('start_date');

        if ($completedProjects->count() > 0) {
            $totalDays = $completedProjects->sum(function ($project) {
                return $project->start_date->diffInDays($project->completed_date);
            });
            $stats['avg_completion_days'] = round($totalDays / $completedProjects->count(), 1);
        } else {
            $stats['avg_completion_days'] = 0;
        }

        // Get recent activity
        $stats['recent_projects'] = $projects->latest()->take(5)->get();

        return $stats;
    }

    /**
     * Get projects by status for user
     */
    public function getProjectsByStatus(int $userId, string $status): \Illuminate\Database\Eloquent\Collection
    {
        return User::find($userId)->getAllInvolvedProjects()
            ->where('status', $status)
            ->with(['client', 'clientUser', 'tasks'])
            ->get();
    }

    /**
     * Get overdue projects for user
     */
    public function getOverdueProjects(int $userId): \Illuminate\Database\Eloquent\Collection
    {
        return User::find($userId)->getAllInvolvedProjects()
            ->where('status', '!=', 'completed')
            ->where('due_date', '<', now())
            ->with(['client', 'clientUser'])
            ->get();
    }

    /**
     * Get projects with upcoming deadlines
     */
    public function getUpcomingDeadlines(int $userId, int $days = 7): \Illuminate\Database\Eloquent\Collection
    {
        return User::find($userId)->getAllInvolvedProjects()
            ->where('status', '!=', 'completed')
            ->whereBetween('due_date', [now(), now()->addDays($days)])
            ->with(['client', 'clientUser'])
            ->orderBy('due_date')
            ->get();
    }

    /**
     * Get project performance metrics
     */
    public function getPerformanceMetrics(int $userId): array
    {
        $projects = User::find($userId)->getAllInvolvedProjects();

        $metrics = [
            'on_time_completion_rate' => 0,
            'budget_adherence_rate' => 0,
            'average_project_score' => 0,
            'client_satisfaction_rate' => 0,
        ];

        $completedProjects = $projects->where('status', 'completed');

        if ($completedProjects->count() > 0) {
            // On-time completion rate
            $onTimeProjects = $completedProjects->filter(function ($project) {
                return $project->due_date && $project->completed_date <= $project->due_date;
            });
            $metrics['on_time_completion_rate'] = round(($onTimeProjects->count() / $completedProjects->count()) * 100, 2);

            // Budget adherence rate
            $budgetProjects = $completedProjects->whereNotNull('budget');
            if ($budgetProjects->count() > 0) {
                $withinBudgetProjects = $budgetProjects->filter(function ($project) {
                    $spent = $project->timeEntries->sum('billable_amount');
                    return $spent <= $project->budget;
                });
                $metrics['budget_adherence_rate'] = round(($withinBudgetProjects->count() / $budgetProjects->count()) * 100, 2);
            }
        }

        return $metrics;
    }

    /**
     * Search projects
     */
    public function searchProjects(int $userId, string $query): \Illuminate\Database\Eloquent\Collection
    {
        return User::find($userId)->getAllInvolvedProjects()
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%");
            })
            ->with(['client', 'clientUser'])
            ->get();
    }

    /**
     * Get projects by client
     */
    public function getProjectsByClient(int $userId, int $clientId): \Illuminate\Database\Eloquent\Collection
    {
        return User::find($userId)->getAllInvolvedProjects()
            ->where('client_id', $clientId)
            ->with(['client', 'tasks', 'timeEntries'])
            ->get();
    }

    /**
     * Get projects by client user
     */
    public function getProjectsByClientUser(int $userId, int $clientUserId): \Illuminate\Database\Eloquent\Collection
    {
        return User::find($userId)->getAllInvolvedProjects()
            ->where('client_user_id', $clientUserId)
            ->with(['clientUser', 'tasks', 'timeEntries'])
            ->get();
    }
}
