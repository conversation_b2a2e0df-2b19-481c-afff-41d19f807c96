<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Step 1: Add portal_user_id to clients table for optional portal access
        Schema::table('clients', function (Blueprint $table) {
            $table->foreignId('portal_user_id')->nullable()->after('user_id')->constrained('users')->onDelete('set null');
            $table->index('portal_user_id');
        });

        // Step 2: Migrate existing client_user_id data to new structure
        // For projects that have client_user_id, create corresponding client records
        DB::statement("
            INSERT INTO clients (user_id, portal_user_id, name, email, created_at, updated_at)
            SELECT
                p.user_id,
                p.client_user_id,
                u.name,
                u.email,
                NOW(),
                NOW()
            FROM projects p
            JOIN users u ON p.client_user_id = u.id
            WHERE p.client_user_id IS NOT NULL
            AND p.client_id IS NULL
            AND NOT EXISTS (
                SELECT 1 FROM clients c
                WHERE c.user_id = p.user_id
                AND c.portal_user_id = p.client_user_id
            )
        ");

        // Step 3: Update projects to reference the new client records
        DB::statement("
            UPDATE projects p
            SET client_id = (
                SELECT c.id
                FROM clients c
                WHERE c.user_id = p.user_id
                AND c.portal_user_id = p.client_user_id
                LIMIT 1
            )
            WHERE p.client_user_id IS NOT NULL
            AND p.client_id IS NULL
        ");

        // Step 4: Remove client_user_id from projects table
        Schema::table('projects', function (Blueprint $table) {
            $table->dropForeign(['client_user_id']);
            $table->dropIndex(['client_user_id', 'status']);
            $table->dropColumn('client_user_id');
        });

        // Step 5: Remove user_type complexity from users table
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['user_type']);
            $table->dropColumn(['user_type', 'can_login_as_client']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore user_type fields
        Schema::table('users', function (Blueprint $table) {
            $table->enum('user_type', ['freelancer', 'client', 'both'])->default('freelancer')->after('email');
            $table->boolean('can_login_as_client')->default(false)->after('user_type');
            $table->index('user_type');
        });

        // Restore client_user_id to projects
        Schema::table('projects', function (Blueprint $table) {
            $table->foreignId('client_user_id')->nullable()->after('client_id')->constrained('users')->onDelete('cascade');
            $table->index(['client_user_id', 'status']);
        });

        // Migrate data back (simplified - may lose some data)
        DB::statement("
            UPDATE projects p
            SET client_user_id = (
                SELECT c.portal_user_id
                FROM clients c
                WHERE c.id = p.client_id
                AND c.portal_user_id IS NOT NULL
                LIMIT 1
            )
            WHERE p.client_id IS NOT NULL
        ");

        // Remove portal_user_id from clients
        Schema::table('clients', function (Blueprint $table) {
            $table->dropForeign(['portal_user_id']);
            $table->dropIndex(['portal_user_id']);
            $table->dropColumn('portal_user_id');
        });
    }
};
