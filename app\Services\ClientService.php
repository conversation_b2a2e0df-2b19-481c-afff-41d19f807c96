<?php

namespace App\Services;

use App\Models\Client;
use App\Models\User;
use App\Repositories\ClientRepository;
use App\Traits\HasCalculations;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Collection;

class ClientService
{
    use HasCalculations;

    protected ClientRepository $clientRepository;

    public function __construct(ClientRepository $clientRepository)
    {
        $this->clientRepository = $clientRepository;
    }

    /**
     * Create a new client
     */
    public function createClient(array $data): Client
    {
        $data['user_id'] = Auth::id();
        return $this->clientRepository->create($data);
    }

    /**
     * Update an existing client
     */
    public function updateClient(Client $client, array $data): bool
    {
        return $this->clientRepository->update($client, $data);
    }

    /**
     * Delete a client
     */
    public function deleteClient(Client $client): array
    {
        if ($client->invoices()->count() > 0 || $client->contracts()->count() > 0) {
            return [
                'success' => false,
                'message' => 'Cannot delete client with existing invoices or contracts.'
            ];
        }

        $this->clientRepository->delete($client);

        return [
            'success' => true,
            'message' => 'Client deleted successfully.'
        ];
    }

    /**
     * Get client with detailed information
     */
    public function getClientDetails(int $clientId): ?Client
    {
        return $this->clientRepository->getWithDetails($clientId);
    }

    /**
     * Get clients for current user
     */
    public function getClientsForUser(int $userId, $request = null)
    {
        return $this->clientRepository->getForUser($userId, $request);
    }

    /**
     * Get clients for dropdown/select
     */
    public function getClientsForSelect(int $userId): Collection
    {
        return $this->clientRepository->getForSelect($userId);
    }

    /**
     * Get top clients by revenue
     */
    public function getTopClientsByRevenue(int $userId, int $limit = 10): Collection
    {
        return $this->clientRepository->getTopClientsByRevenue($userId, $limit);
    }

    /**
     * Get client analytics data
     */
    public function getClientAnalytics(int $userId): array
    {
        $analyticsData = $this->clientRepository->getAnalyticsData($userId);
        
        // Sort by total revenue descending
        usort($analyticsData, function ($a, $b) {
            return $b['total_revenue'] <=> $a['total_revenue'];
        });

        return $analyticsData;
    }

    /**
     * Get client performance metrics
     */
    public function getClientPerformanceMetrics(int $userId): array
    {
        $clients = $this->clientRepository->getAnalyticsData($userId);
        
        $totalRevenue = array_sum(array_column($clients, 'total_revenue'));
        $totalInvoices = array_sum(array_column($clients, 'invoice_count'));
        $totalPending = array_sum(array_column($clients, 'pending_amount'));
        $totalOverdue = array_sum(array_column($clients, 'overdue_amount'));

        return [
            'total_clients' => count($clients),
            'total_revenue' => $totalRevenue,
            'total_invoices' => $totalInvoices,
            'total_pending' => $totalPending,
            'total_overdue' => $totalOverdue,
            'avg_revenue_per_client' => count($clients) > 0 ? $totalRevenue / count($clients) : 0,
            'avg_invoices_per_client' => count($clients) > 0 ? $totalInvoices / count($clients) : 0,
            'top_clients' => array_slice($clients, 0, 5),
        ];
    }

    /**
     * Get clients with payment issues
     */
    public function getClientsWithPaymentIssues(int $userId): array
    {
        $overdueClients = $this->clientRepository->getWithOverdueInvoices($userId);
        $pendingClients = $this->clientRepository->getWithPendingInvoices($userId);

        return [
            'overdue_clients' => $overdueClients->map(function ($client) {
                return [
                    'client' => $client,
                    'overdue_amount' => $client->invoices->sum('total_amount'),
                    'overdue_count' => $client->invoices->count(),
                    'oldest_overdue' => $client->invoices->min('due_date'),
                ];
            }),
            'pending_clients' => $pendingClients->map(function ($client) {
                return [
                    'client' => $client,
                    'pending_amount' => $client->invoices->sum('total_amount'),
                    'pending_count' => $client->invoices->count(),
                ];
            }),
        ];
    }

    /**
     * Get inactive clients
     */
    public function getInactiveClients(int $userId, int $months = 6): Collection
    {
        return $this->clientRepository->getInactiveClients($userId, $months);
    }

    /**
     * Search clients
     */
    public function searchClients(int $userId, string $search): Collection
    {
        return $this->clientRepository->searchByName($userId, $search);
    }

    /**
     * Get client statistics
     */
    public function getClientStats(int $userId): array
    {
        return $this->clientRepository->getStatsForUser($userId);
    }

    /**
     * Calculate client lifetime value
     */
    public function calculateClientLifetimeValue(Client $client): float
    {
        $totalRevenue = $client->invoices()->where('status', 'paid')->sum('total_amount');
        $monthsActive = $client->created_at->diffInMonths(now()) ?: 1;
        
        return $totalRevenue / $monthsActive;
    }

    /**
     * Get client payment patterns
     */
    public function getClientPaymentPatterns(Client $client): array
    {
        $invoices = $client->invoices()->where('status', 'paid')->get();
        
        if ($invoices->isEmpty()) {
            return [
                'avg_payment_time' => 0,
                'payment_reliability' => 0,
                'total_paid' => 0,
                'invoice_count' => 0,
            ];
        }

        $paymentTimes = $invoices->map(function ($invoice) {
            return $invoice->paid_date ? 
                $invoice->due_date->diffInDays($invoice->paid_date, false) : 0;
        });

        $avgPaymentTime = $paymentTimes->avg();
        $onTimePayments = $paymentTimes->filter(function ($days) {
            return $days <= 0; // Paid on or before due date
        })->count();

        return [
            'avg_payment_time' => $avgPaymentTime,
            'payment_reliability' => ($onTimePayments / $invoices->count()) * 100,
            'total_paid' => $invoices->sum('total_amount'),
            'invoice_count' => $invoices->count(),
            'avg_invoice_value' => $invoices->avg('total_amount'),
        ];
    }

    /**
     * Get client revenue trend
     */
    public function getClientRevenueTrend(Client $client, int $months = 12): array
    {
        $invoices = $client->invoices()
            ->where('status', 'paid')
            ->where('created_at', '>=', now()->subMonths($months))
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, SUM(total_amount) as revenue')
            ->groupByRaw('YEAR(created_at), MONTH(created_at)')
            ->orderByRaw('YEAR(created_at), MONTH(created_at)')
            ->get();

        return $invoices->map(function ($item) {
            return [
                'month' => sprintf('%04d-%02d', $item->year, $item->month),
                'revenue' => $item->revenue,
            ];
        })->toArray();
    }

    /**
     * Get users who can act as clients
     */
    public function getClientUsers(): Collection
    {
        return User::clients()->get(['id', 'name', 'email']);
    }

    /**
     * Get client with detailed information
     */
    public function getClientWithDetails(Client $client): array
    {
        $client->load(['invoices' => function ($query) {
            $query->latest()->take(10);
        }, 'contracts' => function ($query) {
            $query->latest()->take(5);
        }]);

        return [
            'payment_patterns' => $this->getClientPaymentPatterns($client),
            'revenue_trend' => $this->getClientRevenueTrend($client),
            'lifetime_value' => $this->calculateClientLifetimeValue($client),
        ];
    }
}
