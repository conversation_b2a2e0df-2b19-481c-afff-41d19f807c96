<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Client extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'portal_user_id',
        'name',
        'email',
        'phone',
        'company_name',
        'address',
        'gst_number',
        'contact_person',
        'default_tds_percentage',
    ];

    protected function casts(): array
    {
        return [
            'default_tds_percentage' => 'float',
        ];
    }

    /**
     * Get the freelancer user that owns the client.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the portal user for client login access (optional).
     */
    public function portalUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'portal_user_id');
    }

    /**
     * Get the invoices for the client.
     */
    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the contracts for the client.
     */
    public function contracts(): HasMany
    {
        return $this->hasMany(Contract::class);
    }

    /**
     * Get the projects for the client.
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    /**
     * Get the TDS records for the client.
     */
    public function tdsRecords(): HasMany
    {
        return $this->hasMany(TdsRecord::class);
    }

    /**
     * Get the follow-ups for the client through invoices.
     */
    public function followUps(): HasManyThrough
    {
        return $this->hasManyThrough(FollowUp::class, Invoice::class);
    }

    /**
     * Check if client has portal access.
     */
    public function hasPortalAccess(): bool
    {
        return !is_null($this->portal_user_id);
    }

    /**
     * Get display name for client.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->company_name ?: $this->name;
    }

    /**
     * Get client type for display.
     */
    public function getClientTypeAttribute(): string
    {
        return $this->hasPortalAccess() ? 'Client with Portal Access' : 'Traditional Client';
    }

}
